<?php

namespace Tests\Unit\Providers;

use App\Providers\ExportServiceProvider;
use App\Services\Externals\Contracts\ExportServiceInterface;
use App\Services\Externals\ExportService;
use GuzzleHttp\Client;
use Tests\TestCase;

/**
 * Test class for ExportServiceProvider
 *
 * Tests the export service provider registration and dependency injection
 * following Laravel testing best practices and ensuring proper service bindings.
 *
 * @covers \App\Providers\ExportServiceProvider
 */
class ExportServiceProviderTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Register the service provider
        $this->app->register(ExportServiceProvider::class);
    }

    /**
     * Test interface is bound to implementation
     */
    public function test_interface_is_bound_to_implementation(): void
    {
        $this->assertTrue($this->app->bound(ExportServiceInterface::class));
        
        $service = $this->app->make(ExportServiceInterface::class);
        
        $this->assertInstanceOf(ExportService::class, $service);
        $this->assertInstanceOf(ExportServiceInterface::class, $service);
    }

    /**
     * Test ExportService is properly registered
     */
    public function test_export_service_is_registered(): void
    {
        $this->assertTrue($this->app->bound(ExportService::class));
        
        $service = $this->app->make(ExportService::class);
        
        $this->assertInstanceOf(ExportService::class, $service);
    }

    /**
     * Test Guzzle Client is registered
     */
    public function test_guzzle_client_is_registered(): void
    {
        $this->assertTrue($this->app->bound(Client::class));
        
        $client = $this->app->make(Client::class);
        
        $this->assertInstanceOf(Client::class, $client);
    }

    /**
     * Test service provider provides correct services
     */
    public function test_service_provider_provides_correct_services(): void
    {
        $provider = new ExportServiceProvider($this->app);
        $provides = $provider->provides();
        
        $this->assertContains(ExportServiceInterface::class, $provides);
        $this->assertContains(ExportService::class, $provides);
    }

    /**
     * Test service instances are created with proper dependencies
     */
    public function test_service_instances_have_proper_dependencies(): void
    {
        $service = $this->app->make(ExportServiceInterface::class);
        
        // Test that the service can perform basic operations
        $this->assertEquals('text/csv', $service->getContentTypeForExport('csv'));
        $this->assertStringEndsWith('.xlsx', $service->getDefaultFilename('xlsx'));
    }

    /**
     * Test service is stateless for Octane compatibility
     */
    public function test_service_is_stateless_for_octane(): void
    {
        // Create multiple instances
        $service1 = $this->app->make(ExportServiceInterface::class);
        $service2 = $this->app->make(ExportServiceInterface::class);
        
        // They should behave identically (stateless)
        $this->assertEquals(
            $service1->getContentTypeForExport('json'),
            $service2->getContentTypeForExport('json')
        );
        
        $this->assertEquals(
            $service1->getDefaultFilename('pdf'),
            $service2->getDefaultFilename('pdf')
        );
    }

    /**
     * Test configuration values are properly used
     */
    public function test_configuration_values_are_used(): void
    {
        // Set test configuration
        config(['services.export_service.url' => 'http://test-export.com']);
        config(['services.export_service.timeout' => 120]);
        
        // Re-register the provider to pick up new config
        $this->app->register(ExportServiceProvider::class, true);
        
        $service = $this->app->make(ExportServiceInterface::class);
        
        // Service should be created successfully with custom config
        $this->assertInstanceOf(ExportService::class, $service);
    }

    /**
     * Test service provider boot method
     */
    public function test_service_provider_boot_method(): void
    {
        $provider = new ExportServiceProvider($this->app);
        
        // Boot method should run without errors
        $provider->boot();
        
        $this->assertTrue(true); // If we get here, boot() didn't throw an exception
    }

    /**
     * Test dependency injection chain works correctly
     */
    public function test_dependency_injection_chain(): void
    {
        // Test that all dependencies can be resolved
        $exportService = $this->app->make(ExportServiceInterface::class);
        $client = $this->app->make(Client::class);
        
        $this->assertInstanceOf(ExportServiceInterface::class, $exportService);
        $this->assertInstanceOf(Client::class, $client);
        
        // Test that services can be used together
        $this->assertIsString($exportService->getContentTypeForExport('csv'));
    }
}
