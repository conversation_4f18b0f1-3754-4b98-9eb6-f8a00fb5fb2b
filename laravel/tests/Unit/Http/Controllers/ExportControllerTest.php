<?php

namespace Tests\Unit\Http\Controllers;

use App\Http\Controllers\ExportController;
use App\Services\Externals\Contracts\ExportServiceInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Mockery;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Tests\TestCase;

/**
 * Test class for ExportController
 *
 * Tests the export controller functionality including request validation,
 * service delegation, and error handling with proper dependency mocking
 * for Laravel Octane compatibility.
 *
 * @covers \App\Http\Controllers\ExportController
 */
class ExportControllerTest extends TestCase
{
    private ExportController $controller;
    private ExportServiceInterface $mockExportService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockExportService = Mockery::mock(ExportServiceInterface::class);
        $this->controller = new ExportController($this->mockExportService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test successful file download
     */
    public function test_download_exported_file_success(): void
    {
        $requestData = [
            'url' => 'http://test.com/download/123',
            'filename' => 'test_export.csv',
            'type' => 'csv'
        ];

        $request = Request::create('/export/download', 'POST', $requestData);
        $mockResponse = Mockery::mock(StreamedResponse::class);

        $this->mockExportService->shouldReceive('getDefaultFilename')
            ->never(); // Should use provided filename

        $this->mockExportService->shouldReceive('getContentTypeForExport')
            ->once()
            ->with('csv')
            ->andReturn('text/csv');

        $this->mockExportService->shouldReceive('downloadExportedFile')
            ->once()
            ->with('http://test.com/download/123', 'test_export.csv', 'text/csv')
            ->andReturn($mockResponse);

        Log::shouldReceive('info')->once();

        $response = $this->controller->downloadExportedFile($request);

        $this->assertSame($mockResponse, $response);
    }

    /**
     * Test file download with default filename
     */
    public function test_download_exported_file_with_default_filename(): void
    {
        $requestData = [
            'url' => 'http://test.com/download/123',
            'type' => 'xlsx'
        ];

        $request = Request::create('/export/download', 'POST', $requestData);
        $mockResponse = Mockery::mock(StreamedResponse::class);

        $this->mockExportService->shouldReceive('getDefaultFilename')
            ->once()
            ->with('xlsx')
            ->andReturn('export_2023-01-01_12-00-00.xlsx');

        $this->mockExportService->shouldReceive('getContentTypeForExport')
            ->once()
            ->with('xlsx')
            ->andReturn('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        $this->mockExportService->shouldReceive('downloadExportedFile')
            ->once()
            ->with(
                'http://test.com/download/123',
                'export_2023-01-01_12-00-00.xlsx',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            ->andReturn($mockResponse);

        Log::shouldReceive('info')->once();

        $response = $this->controller->downloadExportedFile($request);

        $this->assertSame($mockResponse, $response);
    }

    /**
     * Test file download with validation error
     */
    public function test_download_exported_file_validation_error(): void
    {
        $requestData = [
            'url' => 'invalid-url',
            'type' => 'invalid-type'
        ];

        $request = Request::create('/export/download', 'POST', $requestData);

        Log::shouldReceive('warning')->once();

        $this->expectException(ValidationException::class);

        $this->controller->downloadExportedFile($request);
    }

    /**
     * Test file download with service exception
     */
    public function test_download_exported_file_service_exception(): void
    {
        $requestData = [
            'url' => 'http://test.com/download/123',
            'type' => 'csv'
        ];

        $request = Request::create('/export/download', 'POST', $requestData);

        $this->mockExportService->shouldReceive('getDefaultFilename')
            ->once()
            ->andReturn('export.csv');

        $this->mockExportService->shouldReceive('getContentTypeForExport')
            ->once()
            ->andReturn('text/csv');

        $this->mockExportService->shouldReceive('downloadExportedFile')
            ->once()
            ->andThrow(new \Exception('Download failed'));

        Log::shouldReceive('info')->once();
        Log::shouldReceive('error')->once();

        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->expectExceptionMessage('Failed to download file: Download failed');

        $this->controller->downloadExportedFile($request);
    }

    /**
     * Test successful process and download
     */
    public function test_process_and_download_success(): void
    {
        $requestData = [
            'data' => [
                ['id' => 1, 'name' => 'Test 1'],
                ['id' => 2, 'name' => 'Test 2']
            ],
            'type' => 'csv',
            'filename' => 'test_data.csv',
            'options' => ['delimiter' => ';']
        ];

        $request = Request::create('/export/process', 'POST', $requestData);
        $mockResponse = Mockery::mock(StreamedResponse::class);

        $this->mockExportService->shouldReceive('streamExport')
            ->once()
            ->with($requestData['data'], 'csv', ['delimiter' => ';'])
            ->andReturn(['download_url' => 'http://test.com/download/456']);

        $this->mockExportService->shouldReceive('getContentTypeForExport')
            ->once()
            ->with('csv')
            ->andReturn('text/csv');

        $this->mockExportService->shouldReceive('downloadExportedFile')
            ->once()
            ->with('http://test.com/download/456', 'test_data.csv', 'text/csv')
            ->andReturn($mockResponse);

        Log::shouldReceive('info')->twice(); // Start and success

        $response = $this->controller->processAndDownload($request);

        $this->assertSame($mockResponse, $response);
    }

    /**
     * Test process and download with default filename
     */
    public function test_process_and_download_with_default_filename(): void
    {
        $requestData = [
            'data' => [['id' => 1, 'name' => 'Test']],
            'type' => 'json'
        ];

        $request = Request::create('/export/process', 'POST', $requestData);
        $mockResponse = Mockery::mock(StreamedResponse::class);

        $this->mockExportService->shouldReceive('getDefaultFilename')
            ->once()
            ->with('json')
            ->andReturn('export_2023-01-01_12-00-00.json');

        $this->mockExportService->shouldReceive('streamExport')
            ->once()
            ->with($requestData['data'], 'json', [])
            ->andReturn(['download_url' => 'http://test.com/download/789']);

        $this->mockExportService->shouldReceive('getContentTypeForExport')
            ->once()
            ->with('json')
            ->andReturn('application/json');

        $this->mockExportService->shouldReceive('downloadExportedFile')
            ->once()
            ->andReturn($mockResponse);

        Log::shouldReceive('info')->twice();

        $response = $this->controller->processAndDownload($request);

        $this->assertSame($mockResponse, $response);
    }

    /**
     * Test process and download validation error
     */
    public function test_process_and_download_validation_error(): void
    {
        $requestData = [
            'data' => [], // Empty data should fail validation
            'type' => 'csv'
        ];

        $request = Request::create('/export/process', 'POST', $requestData);

        Log::shouldReceive('warning')->once();

        $this->expectException(ValidationException::class);

        $this->controller->processAndDownload($request);
    }

    /**
     * Test process and download with missing download URL
     */
    public function test_process_and_download_missing_download_url(): void
    {
        $requestData = [
            'data' => [['id' => 1, 'name' => 'Test']],
            'type' => 'csv'
        ];

        $request = Request::create('/export/process', 'POST', $requestData);

        $this->mockExportService->shouldReceive('getDefaultFilename')
            ->once()
            ->andReturn('export.csv');

        $this->mockExportService->shouldReceive('streamExport')
            ->once()
            ->andReturn([]); // No download_url

        Log::shouldReceive('info')->once();
        Log::shouldReceive('error')->once();

        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->expectExceptionMessage('Failed to process export: Export service did not return a download URL');

        $this->controller->processAndDownload($request);
    }

    /**
     * Test process and download with invalid argument exception
     */
    public function test_process_and_download_invalid_argument_exception(): void
    {
        $requestData = [
            'data' => [['id' => 1, 'name' => 'Test']],
            'type' => 'csv'
        ];

        $request = Request::create('/export/process', 'POST', $requestData);

        $this->mockExportService->shouldReceive('getDefaultFilename')
            ->once()
            ->andReturn('export.csv');

        $this->mockExportService->shouldReceive('streamExport')
            ->once()
            ->andThrow(new \InvalidArgumentException('Invalid export type'));

        Log::shouldReceive('info')->once();
        Log::shouldReceive('warning')->once();

        $this->expectException(\Symfony\Component\HttpKernel\Exception\HttpException::class);
        $this->expectExceptionMessage('Invalid export parameters: Invalid export type');

        $this->controller->processAndDownload($request);
    }

    /**
     * Test controller uses interface dependency injection
     */
    public function test_controller_uses_interface_dependency_injection(): void
    {
        $reflection = new \ReflectionClass(ExportController::class);
        $constructor = $reflection->getConstructor();
        $parameters = $constructor->getParameters();

        $this->assertCount(1, $parameters);
        $this->assertEquals(ExportServiceInterface::class, $parameters[0]->getType()->getName());
    }
}
