{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "ext-imap": "*", "carlos-meneses/laravel-mpdf": "^2.1", "doctrine/dbal": "^3.2", "gedmo/doctrine-extensions": "^3.4", "guzzlehttp/guzzle": "^7.9", "laravel/framework": "^10.0", "laravel/horizon": "^5.21", "laravel/octane": "^2.5", "laravel/passport": "^11.0", "laravel/sail": "^1.16", "laravel/tinker": "^2.8", "league/flysystem-sftp-v3": "^3.0", "maatwebsite/excel": "^3.1", "openspout/openspout": "^v4.24", "predis/predis": "^2.0", "spatie/laravel-backup": "^8.6", "spatie/laravel-collection-macros": "^7.10", "spatie/laravel-permission": "^5.4", "staudenmeir/belongs-to-through": "^2.11", "staudenmeir/eloquent-has-many-deep": "^1.18", "staudenmeir/laravel-adjacency-list": "^1.13", "staudenmeir/laravel-cte": "*", "symfony/http-client": "^6.3", "symfony/mailgun-mailer": "^6.3", "symfony/postmark-mailer": "^6.3", "webklex/laravel-imap": "^5.3"}, "require-dev": {"fakerphp/faker": "^1.23.0", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "pestphp/pest": "^2.36", "spatie/laravel-ignition": "^2.1"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Database\\Seeders\\Permissions\\": "database/seeders/permissions"}, "files": ["app/Helpers/Helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "stable", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}