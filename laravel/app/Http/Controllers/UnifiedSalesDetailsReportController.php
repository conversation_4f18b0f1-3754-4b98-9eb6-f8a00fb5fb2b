<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Exports\UnifiedSalesExport;
use App\Line;
use App\LineProduct;
use App\Services\SalesDetailsService;
use App\Services\UnifiedSalesDetailsService;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class UnifiedSalesDetailsReportController extends ApiController
{
    public function __construct(private UnifiedSalesDetailsService $salesDetailsService) {}

    public function fetch($saleFilter)
    {
        $authUser = Auth::user();
        return $this->salesDetailsService->fetch($authUser, $saleFilter);
    }

    public function filter(Request $request)
    {

        [$uniqueData, $fields] = $this->fetch($request->saleFilter);

        return response()->json(['data' => collect($uniqueData)->paginate(100), 'fields' => $fields]);
    }


    public function downloadExcel(Request $request)
    {
        /**
         * @var Collection $uniqueData
         */
        [$uniqueData,$_] = $this->fetch($request->saleFilter);


        $fields = array_keys($uniqueData->first()->toArray());

        return Excel::download(
            new UnifiedSalesExport($uniqueData, $fields),
            'sales_report.xlsx'
        );
    }
}
