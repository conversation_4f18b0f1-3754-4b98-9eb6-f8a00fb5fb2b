<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\Externals\ExportService;
use App\Services\SalesAggregationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ExportController extends Controller
{
    /**
     * Export sales aggregation data
     *
     * @param Request $request
     * @param ExportService $exportService
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function exportSalesData(Request $request, ExportService $exportService)
    {
        try {
            // Validate request
            $validated = $request->validate([
                'from' => 'required|date',
                'to' => 'required|date',
                'lines' => 'required|array',
                'lines.*' => 'integer',
                'product_ids' => 'required|array',
                'product_ids.*' => 'integer',
                'distributors' => 'required|array',
                'distributors.*' => 'integer',
                'divisions' => 'required|array',
                'divisions.*' => 'integer',
                'types' => 'required|array',
                'types.*' => 'integer',
                'export_type' => 'required|string|in:csv,xlsx,pdf',
                'download' => 'boolean',
                'filename' => 'nullable|string',
            ]);

            // Create sales aggregation service
            $salesService = new SalesAggregationService($validated['from'], $validated['to']);

            // Get aggregated data
            $dataQuery = $salesService->getOptimizedSalesAggregation(
                $validated['lines'],
                $validated['product_ids'],
                $validated['distributors'],
                $validated['divisions'],
                $validated['types']
            );

            // Get data as array
            $data = $dataQuery->get()->toArray();

            // Log export attempt
            Log::info('Exporting sales data', [
                'rows' => count($data),
                'type' => $validated['export_type']
            ]);

            // Stream export data
            $export = $exportService->streamExport($data, $validated['export_type']);

            // Check if immediate download is requested
            if ($request->input('download', false)) {
                $filename = $validated['filename'] ??
                    $exportService->getDefaultFilename($validated['export_type'], 'sales');

                $contentType = $exportService->getContentTypeForExport($validated['export_type']);

                // Return streamed download
                return $exportService->downloadExportedFile(
                    $export['download_url'],
                    $filename,
                    $contentType
                );
            }

            // Otherwise return export details as JSON
            return response()->json([
                'success' => true,
                'export' => $export,
                'download_url' => route('api.exports.download', [
                    'url' => $export['download_url'],
                    'type' => $validated['export_type'],
                    'filename' => $validated['filename'] ?? null
                ])
            ]);
        } catch (\Exception $e) {
            Log::error('Export failed', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
