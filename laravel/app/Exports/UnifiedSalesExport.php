<?php

namespace App\Exports;

use App\Speciality;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class UnifiedSalesExport implements FromCollection, WithHeadings
{
    protected $data;
    protected $fields;

    /**
     * @param \Illuminate\Support\Collection|array $data
     * @param array $fields
     */
    public function __construct($data, array $fields)
    {
        $this->data = collect($data); // always convert to Collection
        $this->fields = $fields;
    }

    public function collection()
    {
        return $this->data->map(function ($item) {
            return collect($this->fields)->map(function ($field) use ($item) {
                return is_array($item) ? ($item[$field] ?? '') : ($item->{$field} ?? '');
            });
        });
    }

    public function headings(): array
    {
        return $this->fields;
    }
}
