<?php

namespace App\Providers;

use App\Services\Externals\Contracts\ExportServiceInterface;
use App\Services\Externals\ExportService;
use GuzzleHttp\Client;
use Illuminate\Support\ServiceProvider;

/**
 * Export Service Provider
 *
 * Registers export-related services and their dependencies.
 * Follows Laravel Octane compatibility patterns with proper
 * singleton and factory bindings.
 */
class ExportServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        // Register the interface binding
        $this->app->bind(ExportServiceInterface::class, ExportService::class);

        // Register ExportService with proper dependency injection
        $this->app->bind(ExportService::class, function ($app) {
            return new ExportService(
                client: $app->make(Client::class),
                maxRetries: config('services.export_service.max_retries', 3),
                supportedFormats: config('services.export_service.supported_formats', ['csv', 'xlsx', 'pdf', 'json']),
                exportServiceUrl: config('services.export_service.url')
            );
        });

        // Register Guzzle HTTP Client for export service
        $this->app->bind(Client::class, function ($app) {
            return new Client([
                'timeout' => config('services.export_service.timeout', 60),
                'connect_timeout' => config('services.export_service.connect_timeout', 10),
                'verify' => config('services.export_service.verify_ssl', true),
            ]);
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        // Publish configuration if needed
        $this->publishes([
            __DIR__.'/../config/export.php' => config_path('export.php'),
        ], 'config');
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides(): array
    {
        return [
            ExportServiceInterface::class,
            ExportService::class,
        ];
    }
}
