<?php

namespace App\Services;

use App\DivisionType;
use App\Exceptions\CrmException;
use App\Line;
use App\Sale;
use App\SalesSetting;
use App\SalesTypes;
use App\Services\Enums\Ceiling;
use App\Services\Enums\SaleDetailsFieldType;
use App\User;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UnifiedSalesDetailsService
{
    private User $authUser;
    private array $saleFilter;

    private bool $isAchievement = false;
    private Carbon $from;
    private Carbon $to;
    private CarbonPeriod $period;

    private bool $isPerDiv = true;

    private const FIELD_TYPES = [
        "line" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "manager" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "employee" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "emp_code" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "product" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "distributor" => [SaleDetailsFieldType::DETAILS],
        "distribution_type" => [SaleDetailsFieldType::DETAILS],
        "type" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "name" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "code" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "brick" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "pro_price" => [SaleDetailsFieldType::DETAILS],
        "target_units" => [SaleDetailsFieldType::ACHIEVEMENT],
        "units" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "bonus" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "target_value" => [SaleDetailsFieldType::ACHIEVEMENT],
        "value" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "date" => [SaleDetailsFieldType::DETAILS, SaleDetailsFieldType::ACHIEVEMENT],
        "ratio" => [SaleDetailsFieldType::DETAILS],
        "pharmacy_type" => [SaleDetailsFieldType::DETAILS],
        "reference" => [SaleDetailsFieldType::DETAILS],
    ];

    public function generateKey(): string
    {
        return 'filter_data:' .
            '_auth_user:' . $this->authUser->id .
            '_lines:' . implode(',', $this->saleFilter['lines']) .
            '_report_type:' . $this->saleFilter['report_type'] .
            '_users:' . implode(',', $this->saleFilter['users']) .
            '_divisions:' . implode(',', $this->saleFilter['divisions']) .
            '_filter:' . $this->saleFilter['filter'] .
            '_distributors:' . implode(',', $this->saleFilter['distributors']) .
            '_products:' . implode(',', $this->saleFilter['products']) .
            '_type:' . $this->saleFilter['type'] .
            '_fromDate:' . $this->saleFilter['fromDate'] .
            '_toDate:' . $this->saleFilter['toDate'];
    }

    public function initializeDates(): void
    {
        $this->from = Carbon::parse($this->saleFilter['fromDate'])->startOfDay();
        $this->to = Carbon::parse($this->saleFilter['toDate'])->endOfDay();
        $this->period = CarbonPeriod::create($this->from, '1 month', $this->to);
    }

    public function initFilters(): void
    {
        $this->isPerDiv = $this->saleFilter['filter'] == 1;
        $this->isAchievement = $this->saleFilter['report_type'] == 2;

    }

    public function fetch(User $user, $saleFilter): array
    {
        $this->authUser = $user;
        $this->saleFilter = $saleFilter;
        $this->initFilters();
        $this->initializeDates();

        $key = $this->generateKey();
        [$uniqueData, $fields] = Cache::remember(
            $key,
            now()->addHours(2),
            function () use ($saleFilter) {
                $fields = $this->getFields();
                $types = SalesTypes::when(!is_null($saleFilter['type']), fn($q) => $q->where("id", $saleFilter['type']))->pluck('id');
                $division_type = DivisionType::where('last_level', 1)->value('id');
                $isSalesPerDistributor = SalesSetting::where('key', 'mapping_with_distributor')->value('value') === 'Yes';
                $lines = Line::whereIn("id", $saleFilter['lines'])
                    ->with([
                        "distributors" => fn($q) => $q->when($saleFilter['distributors'], fn($q) => $q->whereIn("distributors.id", $saleFilter['distributors'])),
                        "allProducts" => fn($q) => $q->whereIn("line_products.product_id", $saleFilter['products']),
                        "divisions" => fn($q) => $q->when($saleFilter['divisions'], fn($q) => $q->whereIn("line_divisions.id", $saleFilter['divisions'])),
                        "users" => fn($q) => $q->when($saleFilter['users'], fn($q) => $q->whereIn("line_users.user_id", $saleFilter['users']))
                    ])->get();

                $filtered = $lines->flatMap(
                    fn(Line $line) => $this->filterLine($line)
                );

                $belowDivisions = $filtered->flatMap(
                    fn($userOrDiv) => $this->getBelowDivisions($userOrDiv["object"], $userOrDiv["line"], $division_type)
                );


                $productIds = $lines
                    ->pluck("allProducts")
                    ->collapse()
                    ->unique('id')
                    ->pluck("id")
                    ->toArray();

                $distributors = $lines->pluck("distributors")
                    ->collapse()
                    ->unique('id')
                    ->pluck('id')
                    ->toArray();

                $details = $this->details(
                    $lines->pluck('id')->toArray(),
                    $distributors,
                    $productIds,
                    $belowDivisions->toArray(),
                    $types->toArray(),
                    $isSalesPerDistributor
                );

                $uniqueData = $details->unique(fn($item) => $this->getUniqueKey($item))->values();

                return [$uniqueData, $fields];
            }
        );

        return [$uniqueData, $fields];
    }

    private function filterLine(Line $line): Collection
    {

        if ($this->isPerDiv)
            return $this->authUser->filterDivisions(
                $line,
                $line->divisions($this->from, $this->to)
                    ->when(!empty($this->saleFilter['divisions']), fn($q) => $q->whereIn("id", $this->saleFilter['divisions']))
                    ->get(),
                $this->saleFilter,
                $this->from,
                $this->to
            )->map(fn($object) => ['object' => $object, 'line' => $line]);

        return $this->authUser->filterUsers(
            $line,
            $line->users($this->from, $this->to)
                ->when(!empty($this->saleFilter['users']), fn($q) => $q->whereIn("id", $this->saleFilter['users']))
                ->get(),
            $this->saleFilter,
            $this->from,
            $this->to
        )->map(fn($object) => ['object' => $object, 'line' => $line]);

    }

    private function details(
        array $lines,
        array $distributors,
        array $productIds,
        array $belowDivisions,
        array $types,
        bool  $isSalesPerDistributor
    ): Collection
    {
        return $this->getSalesData(
            $lines,
            $productIds,
            $distributors,
            $belowDivisions,
            $types,
            $isSalesPerDistributor
        )
            ->orderBySequence('crm_sales_details.div_id', $belowDivisions)
            ->get();

//        return Sale::hydrate($sales->toArray());
    }

    private function getBelowDivisions($object, $line, $division_type)
    {
        if ($this->isPerDiv) {
            return $object->getBelowDivisions()->where('division_type_id', $division_type)->where('is_kol', 0)->unique('id')->pluck('id')->toArray();
        }
        return $object->allBelowDivisions($line)->where('division_type_id', $division_type)->where('is_kol', 0)->unique('id')->pluck('id')->toArray();
    }

    private function getSalesData(
        array $lines,
        array $productIds,
        array $distributors,
        array $belowDivisions,
        array $types,
        bool  $isSalesPerDistributor
    )
    {
        $dates = [];
        foreach ($this->period as $date) {
            $dates[] = $date;
        }
        return $this->buildSalesQuery($lines, $productIds, $distributors, $belowDivisions, $dates, $types, $isSalesPerDistributor);
    }

    private function getAvgProductPriceQuery(): string
    {
        return 'COALESCE(
            (
                SELECT avg_price
                FROM crm_product_prices
                WHERE crm_product_prices.product_id = crm_sales.product_id
                AND crm_product_prices.deleted_at IS NULL
                AND crm_product_prices.distributor_id = crm_sales.distributor_id
                AND crm_product_prices.from_date <= crm_sales_details.date
                AND (crm_product_prices.to_date IS NULL OR crm_product_prices.to_date >= crm_sales_details.date)
                LIMIT 1
            ),
            (
                SELECT avg_price
                FROM crm_product_prices
                WHERE crm_product_prices.product_id = crm_sales.product_id
                AND crm_product_prices.deleted_at IS NULL
                AND crm_product_prices.distributor_id IS NULL
                AND crm_product_prices.from_date <= crm_sales_details.date
                AND (crm_product_prices.to_date IS NULL OR crm_product_prices.to_date >= crm_sales_details.date)
                LIMIT 1
            )
        )';
    }

    public function getSelectionsForDetails(): array
    {
        $product_avg_price = $this->getAvgProductPriceQuery();

        return [
            DB::raw("GROUP_CONCAT(crm_sales.id) as id"),
            DB::raw("''  as detail_id"),
            'lines.name as line',
            'line_divisions.name as division',
            'sales_details.div_id',
            DB::raw('IFNULL(crm_sales.distributor_id,"") as distributor_id'),
            DB::raw('IFNULL(crm_distributors.name,"") AS distributor'),
            DB::raw("'' as brick"),
            DB::raw("'' as brick_id"),
            DB::raw('DATE_FORMAT(crm_sales_details.date, "%Y-%m-%d") as date'),
            DB::raw('MAX(IFNULL(crm_higher.fullname, "")) as manager'),

            // Employee subquery
            DB::raw("MAX((SELECT IFNULL(crm_users.fullname, '')
                  FROM crm_users
                  JOIN crm_line_users_divisions ON crm_users.id = crm_line_users_divisions.user_id
                  WHERE crm_line_users_divisions.line_division_id = crm_sales_details.div_id
                    AND crm_line_users_divisions.deleted_at IS NULL
                    AND crm_users.deleted_at IS NULL
                    AND crm_line_users_divisions.from_date <= crm_sales_details.date
                    AND (crm_line_users_divisions.to_date IS NULL OR crm_line_users_divisions.to_date >= crm_sales_details.date)
                  LIMIT 1)) as employee"
            ),

            DB::raw("MAX((SELECT IFNULL(crm_users.emp_code, '')
                  FROM crm_users
                  JOIN crm_line_users_divisions ON crm_users.id = crm_line_users_divisions.user_id
                  WHERE crm_line_users_divisions.line_division_id = crm_sales_details.div_id
                    AND crm_line_users_divisions.deleted_at IS NULL
                    AND crm_users.deleted_at IS NULL
                    AND crm_line_users_divisions.from_date <= crm_sales_details.date
                    AND (crm_line_users_divisions.to_date IS NULL OR crm_line_users_divisions.to_date >= crm_sales_details.date)
                  LIMIT 1)) as emp_code"
            ),
            DB::raw('MAX(crm_sales_types.name) as type'),
            DB::raw("'BULK' AS name"),
            DB::raw("'' AS code"),
            DB::raw("'' AS pharmacy_type"),
            DB::raw(
                "ANY_VALUE('DISTRIBUTED') as distribution_type"
            ),
            'sales.product_id',
            'products.name as product',
            DB::raw('MAX(crm_division_types.color) as color'),
            DB::raw('FORMAT(SUM(crm_sales_details.quantity),2) as units'),
            DB::raw("FORMAT(MAX($product_avg_price),2) as pro_price"),
            DB::raw('SUM(crm_sales_details.bonus) as bonus'),
            DB::raw("
                    FORMAT(SUM(
                        CASE
                            WHEN crm_sales.value > 0 THEN crm_sales_details.value
                            ELSE $product_avg_price * crm_sales_details.quantity
                        END
                       )
                    , 2) as value
                   "
            ),
            DB::raw('SUM(crm_sales_details.ratio) as ratio'),
            DB::raw("GROUP_CONCAT(DISTINCT(crm_sales.sale_ids)) AS referenceId"),
        ];

    }

    public function getSelectionsForDetailsInNormalMode(): array
    {
        $product_avg_price = $this->getAvgProductPriceQuery();

        return [
            'sales.id as id',
            'sales_details.id as detail_id',
            'lines.name as line',
            'line_divisions.name as division',
            'sales_details.div_id as div_id',
            DB::raw('IFNULL(crm_sales.distributor_id,"") as distributor_id'),
            DB::raw('IFNULL(crm_distributors.name,"") AS distributor'),
            DB::raw('IFNULL(crm_bricks.name,"") AS brick'),
            DB::raw('IFNULL(crm_sales_details.brick_id,"") AS brick_id'),
            DB::raw('DATE_FORMAT(crm_sales_details.date,"%Y-%m-%d") as date'),
            DB::raw('IFNULL(crm_higher.fullname,"") AS manager'),
            DB::raw(
                '(SELECT  IFNULL(crm_users.fullname,"")
                FROM crm_users
                JOIN crm_line_users_divisions ON crm_users.id = crm_line_users_divisions.user_id
                WHERE crm_line_users_divisions.line_division_id = crm_sales_details.div_id
                AND crm_line_users_divisions.deleted_at IS NULL
                AND crm_users.deleted_at IS NULL
                AND crm_line_users_divisions.from_date <= crm_sales_details.date
                AND (crm_line_users_divisions.to_date IS NULL OR crm_line_users_divisions.to_date >= crm_sales_details.date)
                LIMIT 1) as employee'
            ),
            DB::raw(
                '(SELECT  IFNULL(crm_users.emp_code,"")
                FROM crm_users
                JOIN crm_line_users_divisions ON crm_users.id = crm_line_users_divisions.user_id
                WHERE crm_line_users_divisions.line_division_id = crm_sales_details.div_id
                AND crm_line_users_divisions.deleted_at IS NULL
                AND crm_users.deleted_at IS NULL
                AND crm_line_users_divisions.from_date <= crm_sales_details.date
                AND (crm_line_users_divisions.to_date IS NULL OR crm_line_users_divisions.to_date >= crm_sales_details.date)
                LIMIT 1) as emp_code'
            ),
            'sales_types.name as type',
            DB::raw("COALESCE(crm_mapping_unified_codes.name, CONCAT('DIST: ', crm_mappings.name)) AS name"),
            DB::raw("COALESCE(crm_mapping_unified_codes.code, CONCAT('DIST: ', crm_mappings.code)) AS code"),
            DB::raw("COALESCE(crm_unified_pharmacy_types.name, '') AS pharmacy_type"),
            DB::raw(
                "
                   CASE
                     WHEN crm_sales.ceiling = '0' THEN 'NORMAL'
                     WHEN crm_sales.ceiling = '2' THEN 'DISTRIBUTED'
                   END as distribution_type"
            ),
            'sales.product_id',
            'products.name as product',
            'division_types.color as color',
            DB::raw('crm_sales_details.quantity as units'),
            DB::raw("FORMAT($product_avg_price,2) as pro_price"),
            DB::raw('crm_sales_details.bonus as bonus'),
            DB::raw("
                   CASE
                       WHEN crm_sales.value = 0 THEN FORMAT(($product_avg_price * crm_sales_details.quantity),2)
                       ELSE FORMAT(crm_sales_details.value,2)
                   END as value
                   "
            ),
            'sales_details.ratio',
            'sales.sale_ids as referenceId',
        ];

    }

    private function buildSalesQuery(
        array $lines,
        array $productIds,
        array $distributors,
        array $belowDivisions,
        array $dates,
        array $types,
        bool  $isSalesPerDistributor
    )
    {

        // Query for Ceiling::BELOW
        //        // Query for Ceiling::DISTRIBUTED - with distributor grouping
//        $distributedQuery = $this->buildCeilingQuery(
//            Ceiling::DISTRIBUTED,
//            $this->getSelectionsForDetails(),
//            $lines,
//            $productIds,
//            $distributors,
//            $belowDivisions,
//            $dates,
//            $types,
//            $isSalesPerDistributor
//        );


        // For DISTRIBUTED, add grouping by distributor if it's an achievement query

//        $groups = [
//            'sales.distributor_id',
//            'distributors.name',
//            'sales_details.div_id',
//            'line_divisions.name',
//            'lines.id',
//            'lines.name',
//            'sales_details.date',
//            'sales.product_id',
//            'products.name',
//        ];
//        $distributedQuery->groupBy($groups);


//        return DB::query()
//            ->withExpression('first_part', $belowQuery)
//            ->withExpression('second_part', $distributedQuery)
//            ->from(DB::raw("(SELECT * FROM crm_first_part UNION SELECT * FROM crm_second_part) as sub"));

        return $this->buildCeilingQuery(
            [Ceiling::DISTRIBUTED, Ceiling::BELOW],
            $this->getSelectionsForDetailsInNormalMode(),
            $lines,
            $productIds,
            $distributors,
            $belowDivisions,
            $dates,
            $types,
            $isSalesPerDistributor
        );
    }

    /**
     * Build a query for a specific ceiling type
     */
    private function buildCeilingQuery(
        array   $ceilingTypes,
        array   $selections,
        array   $lineIds,
        array   $productIds,
        array   $distributorIds,
        array   $belowDivisions,
        array   $dates,
        array   $types,
        bool    $isSalesPerDistributor
    )
    {
        $query = Sale::select($selections)
            ->whereIn("ceiling",$ceilingTypes)
            ->whereIn('sales.date', $dates)
            ->join('products', fn($join) => $join->on('sales.product_id', 'products.id')->whereIn('sales.product_id', $productIds))
            ->join('sales_details', fn($join) => $join->on('sales.id', 'sales_details.sale_id')->whereIn('sales_details.div_id', $belowDivisions)
                ->whereIn('sales_details.date', $dates))
            ->leftJoin('mapping_sale', 'sales.id', 'mapping_sale.sale_id')
            ->leftJoin('target_details', function ($join) {
                $join->on('sales.product_id', 'target_details.product_id')
                    ->whereColumn('sales_details.div_id', 'target_details.div_id')
                    ->whereColumn('sales_details.brick_id', 'target_details.brick_id')
                    ->whereColumn('sales_details.date', 'target_details.date');
            })
            ->join(
                'mappings',
                fn($join) => $join->on('mapping_sale.mapping_id', 'mappings.id')
                    ->whereIn('mappings.mapping_type_id', $types)
                    ->where(fn($q) => $q->whereIn('mappings.line_id', $lineIds)->orWhereNull('mappings.line_id'))
                    ->where(fn($q) => $q->whereIn('mappings.distributor_id', $distributorIds)->orWhereNull('mappings.distributor_id'))
            )
            ->leftJoin('unified_pharmacy_types', 'unified_pharmacy_types.id', 'mappings.pharmacy_type_id')
            ->leftJoin('mapping_unified_codes', 'mapping_unified_codes.code', 'mappings.uc_code');

        if ($isSalesPerDistributor) {
            $query->leftJoin('distributors', 'mappings.distributor_id', 'distributors.id');
        } else {
            $query->leftJoin('distributors', 'sales.distributor_id', 'distributors.id');
        }

        return $query
            ->leftJoin('sales_types', 'mappings.mapping_type_id', 'sales_types.id')
            ->leftJoin('bricks', 'sales_details.brick_id', 'bricks.id')
            ->leftJoin('line_divisions', 'sales_details.div_id', 'line_divisions.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin(
                'line_users_divisions as low_level',
                fn($join) => $join->on('line_divisions.id', 'low_level.line_division_id')
                    ->whereNull('low_level.deleted_at')
                    ->where(function ($query) {
                        $query->where(function ($subQuery) {
                            $subQuery->whereNull('low_level.to_date') // Active records
                            ->orWhereBetween('low_level.to_date', [$this->from->toDateString(), $this->to->toDateString()]) // Ends within range
                            ->orWhere('low_level.to_date', '>=', $this->to->toDateString()); // Ends within range
                        })
                            ->where(function ($subQuery) {
                                $subQuery->where('low_level.from_date', '<=', $this->from->toDateString()) // Starts before range
                                ->orWhereBetween('low_level.from_date', [$this->from->toDateString(), $this->to->toDateString()]); // Starts within range
                            });
                    })
            )
            ->leftJoin('users as mr', 'low_level.user_id', 'mr.id')
            ->leftJoin(
                'line_div_parents',
                fn($join) => $join->on('line_divisions.id', 'line_div_parents.line_div_id')
                    ->whereNull('line_div_parents.deleted_at')
                    ->where(function ($query) {
                        $query->where(function ($subQuery) {
                            $subQuery->whereNull('line_div_parents.to_date') // Active records
                            ->orWhereBetween('line_div_parents.to_date', [$this->from->toDateString(), $this->to->toDateString()]) // Ends within range
                            ->orWhere('line_div_parents.to_date', '>=', $this->to->toDateString()); // Ends within range
                        })
                            ->where(function ($subQuery) {
                                $subQuery->where('line_div_parents.from_date', '<=', $this->from->toDateString()) // Starts before range
                                ->orWhereBetween('line_div_parents.from_date', [$this->from->toDateString(), $this->to->toDateString()]); // Starts within range
                            });
                    })
            )
            ->leftJoin('line_divisions as parent', 'line_div_parents.parent_id', 'parent.id')
            ->leftJoin(
                'line_users_divisions as high_level',
                fn($join) => $join->on('line_div_parents.parent_id', 'high_level.line_division_id')
                    ->where(function ($query) {
                        $query->where(function ($subQuery) {
                            $subQuery->whereNull('high_level.to_date') // Active records
                            ->orWhereBetween('high_level.to_date', [$this->from->toDateString(), $this->to->toDateString()]) // Ends within range
                            ->orWhere('high_level.to_date', '>=', $this->to->toDateString()); // Ends within range
                        })
                            ->where(function ($subQuery) {
                                $subQuery->where('high_level.from_date', '<=', $this->from->toDateString()) // Starts before range
                                ->orWhereBetween('high_level.from_date', [$this->from->toDateString(), $this->to->toDateString()]); // Starts within range
                            });
                    })
            )
            ->leftJoin('users as higher', 'high_level.user_id', 'higher.id')
            ->leftJoin('lines', 'line_divisions.line_id', 'lines.id');

    }


    private function getFields(): array
    {

        $fields = array_map(fn($types) => SaleDetailsFieldType::combine(...$types), self::FIELD_TYPES);

        if ($this->isPerDiv) {
            $fields = array_merge(
                ["division" =>
                    SaleDetailsFieldType::combine(
                        SaleDetailsFieldType::DETAILS,
                        SaleDetailsFieldType::ACHIEVEMENT
                    )
                ],
                $fields
            );
        }

        return SaleDetailsFieldType::getItemsByType($fields, SaleDetailsFieldType::DETAILS);
    }


    private function getUniqueKey(Sale $item): array
    {

        return [
            $item->id,
            $item->detail_id,
            $item->product_id,
            $item->line,
            $item->division,
            $item->employee,
            $item->distributor_id,
            $item->type
        ];
    }
}

