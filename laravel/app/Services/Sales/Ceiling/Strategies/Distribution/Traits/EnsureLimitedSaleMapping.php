<?php


namespace App\Services\Sales\Ceiling\Strategies\Distribution\Traits;

use App\Sale;

trait EnsureLimitedSaleMapping{
    /**
     * Override to ensure limited sale is properly attached to mapping
     *
     * @param mixed $ceilingSale
     * @param Sale $originalSale
     * @return bool
     */
    protected function createLimitedSaleDistribution($ceilingSale, Sale $originalSale): bool
    {
        if ($originalSale->quantity == 0) {
            return false;
        }

        $limitQuantity = $this->limitCalculator->calculateLimit($ceilingSale);
        $limitedSale = $this->saleCreator->createLimitedSale($ceilingSale, $originalSale, $limitQuantity);

        // Attach the limited sale to the mapping for proper referential integrity
        $this->saleCreator->attachMapping($limitedSale, $ceilingSale->mapping_id);

        return $this->saleDetailFactory->createLimitedSaleDetails($originalSale, $limitedSale, $ceilingSale);
    }
}
