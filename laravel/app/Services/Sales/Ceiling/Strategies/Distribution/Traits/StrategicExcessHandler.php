<?php

namespace App\Services\Sales\Ceiling\Strategies\Distribution\Traits;



use App\Sale;
use Illuminate\Support\Facades\Log;

trait StrategicExcessHandler {
    /**
     * Create and distribute excess sale using simple distribution
     *
     * @param mixed $ceilingSale
     * @param Sale $originalSale
     * @param array $salesContributionBaseOn
     * @return bool
     * @throws \Exception
     */
    protected function createAndDistributeExcessSale($ceilingSale, Sale $originalSale, array $salesContributionBaseOn): bool
    {
        $className = self::class;

        $excessQuantity = $this->excessDistributor->calculateExcessQuantity($ceilingSale);

        try {
            $excessSale = $this->saleCreator->createExcessSale($ceilingSale, $excessQuantity);
            $excessSale = $this->saleCreator->loadRelationships($excessSale);
        } catch (\Throwable $e) {
            Log::error("$className: Failed to create excess sale", [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                'error_message' => $e->getMessage()
            ]);
            throw new \Exception('Failed to create excess sale: ' . $e->getMessage());
        }

        $distributionSuccess = $this->excessDistributor->distributeExcessSale(
            $excessSale,
            $salesContributionBaseOn,
            $originalSale,
            $this->getDistributionType()
        );

        if ($distributionSuccess) {
            // Additional validation: Verify sale details were actually created
            $validationResult = $this->validateDistributionIntegrity($excessSale, $ceilingSale);
            if ($validationResult['valid']){
                try{
                    $this->saleCreator->attachMapping($excessSale, $ceilingSale->mapping_id);
                }catch (\Throwable $e){
                    Log::error("$className: Failed to attach mapping", [
                        'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                        'sale_id' => $excessSale->id,
                        'error_message' => $e->getMessage()
                    ]);
                    // Don't fail the entire process for mapping attachment failure
                }
                return true;
            }else {
                Log::error("$className: Distribution validation failed", [
                    'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                    'sale_id' => $excessSale->id,
                    'validation_details' => $validationResult
                ]);
                return false;
            }
        } else {
            Log::error("$className: Distribution algorithm failed", [
                'ceiling_sale_id' => $ceilingSale->id ?? 'unknown',
                'sale_id' => $excessSale->id,
                'quantity' => $excessSale->quantity,
                'reason' => "$className returned false"
            ]);
            return false;
        }


    }
}
