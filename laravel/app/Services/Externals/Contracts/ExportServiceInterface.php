<?php

namespace App\Services\Externals\Contracts;

use GuzzleHttp\Promise\PromiseInterface;
use Symfony\Component\HttpFoundation\StreamedResponse;

/**
 * Interface for Export Service
 *
 * This interface defines the contract for export services that handle
 * data export operations, file downloads, and streaming functionality.
 * Implementations should be stateless and compatible with Laravel Octane.
 */
interface ExportServiceInterface
{
    /**
     * Stream data for export
     *
     * @param iterable $dataRows The data to export
     * @param string $exportType The export format (csv, xlsx, pdf, json)
     * @param array $options Additional export options
     * @return array Export result containing download_url and other metadata
     * @throws \Exception When export fails
     */
    public function streamExport(iterable $dataRows, string $exportType = 'csv', array $options = []): array;

    /**
     * Asynchronous export for large datasets
     *
     * @param iterable $dataRows The data to export
     * @param string $exportType The export format (csv, xlsx, pdf, json)
     * @param array $options Additional export options
     * @return PromiseInterface Promise that resolves to export result
     */
    public function asyncExport(iterable $dataRows, string $exportType = 'csv', array $options = []): PromiseInterface;

    /**
     * Export with pagination for very large datasets
     *
     * @param callable $dataProvider Function that accepts offset and limit and returns a chunk of data
     * @param int $totalCount Total number of records
     * @param int $chunkSize Size of each chunk
     * @param string $exportType Type of export
     * @param array $options Additional options
     * @return array Export result containing download_url and other metadata
     * @throws \Exception When export fails
     */
    public function paginatedExport(
        callable $dataProvider,
        int $totalCount,
        int $chunkSize = 5000,
        string $exportType = 'csv',
        array $options = []
    ): array;

    /**
     * Check export progress using job ID
     *
     * @param string $jobId The export job identifier
     * @return array Progress information
     * @throws \Exception When progress check fails
     */
    public function checkExportProgress(string $jobId): array;

    /**
     * Get default filename for export type
     *
     * @param string $exportType The export format (csv, xlsx, pdf, json)
     * @return string Default filename with appropriate extension
     */
    public function getDefaultFilename(string $exportType): string;

    /**
     * Get content type for export format
     *
     * @param string $exportType The export format (csv, xlsx, pdf, json)
     * @return string MIME content type
     * @throws \InvalidArgumentException When export type is not supported
     */
    public function getContentTypeForExport(string $exportType): string;

    /**
     * Download exported file from URL
     *
     * @param string $downloadUrl The URL to download from
     * @param string $filename The filename for the download
     * @param string $contentType The MIME content type
     * @return StreamedResponse Streamed download response
     * @throws \Exception When download fails
     */
    public function downloadExportedFile(string $downloadUrl, string $filename, string $contentType): StreamedResponse;
}
