<?php

namespace App\Services\Externals;

use App\Services\Externals\Contracts\ExportServiceInterface;
use Guz<PERSON><PERSON>ttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Promise\PromiseInterface;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Psr7\Utils;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;
use Symfony\Component\HttpFoundation\StreamedResponse;

/**
 * Export Service Implementation
 *
 * This service handles data export operations, file downloads, and streaming functionality.
 * It is designed to be stateless and compatible with Laravel Octane.
 */
class ExportService implements ExportServiceInterface
{
    /** @var Client */
    private readonly Client $client;

    /** @var int */
    private readonly int $maxRetries;

    /** @var array */
    private readonly array $supportedFormats;

    /** @var string */
    private readonly string $exportServiceUrl;

    /**
     * ExportService constructor with dependency injection
     *
     * @param Client|null $client HTTP client for making requests
     * @param int $maxRetries Maximum number of retry attempts
     * @param array $supportedFormats Supported export formats
     * @param string|null $exportServiceUrl Export service URL
     */
    public function __construct(
        ?Client $client = null,
        int $maxRetries = 3,
        array $supportedFormats = ['csv', 'xlsx', 'pdf', 'json'],
        ?string $exportServiceUrl = null
    ) {
        $this->maxRetries = $maxRetries;
        $this->supportedFormats = $supportedFormats;
        $this->exportServiceUrl = $exportServiceUrl ?? config('services.export_service.url', 'http://localhost:7000');

        if ($client) {
            $this->client = $client;
        } else {
            $this->client = $this->createDefaultClient();
        }

        // Validate configuration
        $this->validateConfiguration();

        Log::debug('ExportService initialized', [
            'max_retries' => $this->maxRetries,
            'supported_formats' => $this->supportedFormats,
            'export_service_url' => $this->exportServiceUrl,
            'context' => 'SERVICE_INITIALIZATION'
        ]);
    }

    /**
     * Validate export service configuration
     *
     * @throws \InvalidArgumentException
     */
    private function validateConfiguration(): void
    {
        if (empty($this->exportServiceUrl)) {
            throw new \InvalidArgumentException('Export service URL is not configured. Please set EXPORT_SERVICE_URL in your environment.');
        }

        if (!filter_var($this->exportServiceUrl, FILTER_VALIDATE_URL)) {
            throw new \InvalidArgumentException('Export service URL is not valid: ' . $this->exportServiceUrl);
        }

        if (empty($this->supportedFormats)) {
            throw new \InvalidArgumentException('No supported export formats configured.');
        }
    }

    /**
     * Create default HTTP client with retry middleware
     *
     * @return Client
     */
    private function createDefaultClient(): Client
    {
        $stack = HandlerStack::create();
        $stack->push($this->retryMiddleware());

        return new Client([
            'handler' => $stack,
            'timeout' => config('services.export_service.timeout', 60),
            'connect_timeout' => config('services.export_service.connect_timeout', 10),
        ]);
    }

    /**
     * Stream data for export
     *
     * @param iterable $dataRows
     * @param string $exportType
     * @param array $options
     * @return array
     * @throws \Exception
     */
    public function streamExport(iterable $dataRows, string $exportType = 'csv', array $options = []): array
    {
        $this->validateExportType($exportType);

        try {
            Log::info('Starting export process', ['type' => $exportType, 'options' => $options]);

            $stream = Utils::streamFor((function () use ($dataRows) {
                foreach ($dataRows as $row) {
                    yield json_encode($row) . "\n";
                }
            })());

            $headers = [
                'Content-Type' => 'application/x-ndjson',
                'X-Export-Type' => $exportType,
            ];

            // Add any custom headers from options
            if (isset($options['headers']) && is_array($options['headers'])) {
                $headers = array_merge($headers, $options['headers']);
            }

            $requestOptions = [
                'headers' => $headers,
                'body' => $stream,
            ];

            // Add any additional request options
            if (isset($options['request_options']) && is_array($options['request_options'])) {
                $requestOptions = array_merge($requestOptions, $options['request_options']);
            }

            $response = $this->client->request(
                'POST',
                $this->exportServiceUrl . '/export',
                $requestOptions
            );

            $result = json_decode($response->getBody(), true);

            Log::info('Export completed successfully', [
                'type' => $exportType,
                'download_url' => $result['download_url'] ?? null
            ]);

            return $result;
        } catch (GuzzleException $e) {
            Log::error('Export service error', [
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'type' => $exportType
            ]);

            throw new \Exception('Failed to export data: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Asynchronous export for large datasets
     *
     * @param iterable $dataRows
     * @param string $exportType
     * @param array $options
     * @return PromiseInterface
     */
    public function asyncExport(iterable $dataRows, string $exportType = 'csv', array $options = []): PromiseInterface
    {
        $this->validateExportType($exportType);

        Log::info('Starting async export process', ['type' => $exportType]);

        $stream = Utils::streamFor((function () use ($dataRows) {
            foreach ($dataRows as $row) {
                yield json_encode($row) . "\n";
            }
        })());

        $headers = [
            'Content-Type' => 'application/x-ndjson',
            'X-Export-Type' => $exportType,
            'X-Async' => 'true'
        ];

        // Add any custom headers from options
        if (isset($options['headers']) && is_array($options['headers'])) {
            $headers = array_merge($headers, $options['headers']);
        }

        $request = new Request(
            'POST',
            $this->exportServiceUrl . '/export',
            $headers,
            $stream
        );

        return $this->client->sendAsync($request)
            ->then(
                function (ResponseInterface $response) use ($exportType) {
                    Log::info('Async export completed', ['type' => $exportType]);
                    return json_decode($response->getBody(), true);
                },
                function (\Exception $e) use ($exportType) {
                    Log::error('Async export failed', [
                        'type' => $exportType,
                        'message' => $e->getMessage()
                    ]);
                    throw $e;
                }
            );
    }

    /**
     * Export with pagination for very large datasets
     *
     * @param callable $dataProvider Function that accepts offset and limit and returns a chunk of data
     * @param int $totalCount Total number of records
     * @param int $chunkSize Size of each chunk
     * @param string $exportType Type of export
     * @param array $options Additional options
     * @return array
     * @throws \Exception
     */
    public function paginatedExport(
        callable $dataProvider,
        int $totalCount,
        int $chunkSize = 5000,
        string $exportType = 'csv',
        array $options = []
    ): array {
        $this->validateExportType($exportType);

        try {
            Log::info('Starting paginated export', [
                'type' => $exportType,
                'total' => $totalCount,
                'chunk_size' => $chunkSize
            ]);

            $headers = [
                'Content-Type' => 'application/x-ndjson',
                'X-Export-Type' => $exportType,
                'X-Paginated' => 'true',
                'X-Total-Count' => $totalCount
            ];

            // Add any custom headers from options
            if (isset($options['headers']) && is_array($options['headers'])) {
                $headers = array_merge($headers, $options['headers']);
            }

            $response = $this->client->request(
                'POST',
                $this->exportServiceUrl . '/paginated-export/init',
                ['headers' => $headers]
            );

            $initResult = json_decode($response->getBody(), true);
            $exportId = $initResult['export_id'] ?? null;

            if (!$exportId) {
                throw new \Exception('Failed to initialize paginated export: No export ID returned');
            }

            // Process chunks
            for ($offset = 0; $offset < $totalCount; $offset += $chunkSize) {
                $dataChunk = $dataProvider($offset, $chunkSize);

                $stream = Utils::streamFor((function () use ($dataChunk) {
                    foreach ($dataChunk as $row) {
                        yield json_encode($row) . "\n";
                    }
                })());

                $chunkHeaders = [
                    'Content-Type' => 'application/x-ndjson',
                    'X-Export-ID' => $exportId,
                    'X-Chunk-Offset' => $offset,
                    'X-Chunk-Size' => $chunkSize
                ];

                $this->client->request(
                    'POST',
                    $this->exportServiceUrl . '/paginated-export/chunk',
                    [
                        'headers' => $chunkHeaders,
                        'body' => $stream
                    ]
                );

                Log::info('Exported chunk', ['offset' => $offset, 'size' => $chunkSize]);
            }

            // Finalize export
            $response = $this->client->request(
                'POST',
                $this->exportServiceUrl . '/paginated-export/finalize',
                ['headers' => ['X-Export-ID' => $exportId]]
            );

            $result = json_decode($response->getBody(), true);

            Log::info('Paginated export completed', [
                'type' => $exportType,
                'download_url' => $result['download_url'] ?? null
            ]);

            return $result;
        } catch (GuzzleException $e) {
            Log::error('Paginated export failed', [
                'message' => $e->getMessage(),
                'code' => $e->getCode()
            ]);

            throw new \Exception('Failed to complete paginated export: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Check export progress using job ID
     *
     * @param string $jobId
     * @return array
     * @throws \Exception
     */
    public function checkExportProgress(string $jobId): array
    {
        try {
            $response = $this->client->request(
                'GET',
                $this->exportServiceUrl . "/export/{$jobId}/status"
            );

            return json_decode($response->getBody(), true);
        } catch (GuzzleException $e) {
            Log::error('Failed to check export progress', [
                'job_id' => $jobId,
                'message' => $e->getMessage()
            ]);

            throw new \Exception('Failed to check export progress: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Create retry middleware for Guzzle
     *
     * @return callable
     */
    private function retryMiddleware(): callable
    {
        return Middleware::retry(
            function (
                $retries,
                Request $request,
                ?Response $response = null,
                ?\Exception $exception = null
            ) {
                // Suppress unused parameter warning for $request
                unset($request);
                // Don't retry if we've reached the max retries
                if ($retries >= $this->maxRetries) {
                    return false;
                }

                // Retry on connection exceptions
                if ($exception instanceof \GuzzleHttp\Exception\ConnectException) {
                    Log::warning('Retrying export request after connection error', [
                        'attempt' => $retries + 1,
                        'max_retries' => $this->maxRetries,
                        'message' => $exception->getMessage()
                    ]);
                    return true;
                }

                // Retry on 5xx server errors
                if ($response && $response->getStatusCode() >= 500) {
                    Log::warning('Retrying export request after server error', [
                        'attempt' => $retries + 1,
                        'max_retries' => $this->maxRetries,
                        'status_code' => $response->getStatusCode()
                    ]);
                    return true;
                }

                return false;
            },
            function ($retries) {
                // Exponential backoff with jitter: 2^retries * 100ms + random milliseconds
                return (2 ** $retries) * 1000 + random_int(0, 1000);
            }
        );
    }

    /**
     * Get default filename for export type
     *
     * @param string $exportType The export format (csv, xlsx, pdf, json)
     * @return string Default filename with appropriate extension
     */
    public function getDefaultFilename(string $exportType): string
    {
        $this->validateExportType($exportType);

        $timestamp = now()->format('Y-m-d_H-i-s');

        return "export_{$timestamp}.{$exportType}";
    }

    /**
     * Get content type for export format
     *
     * @param string $exportType The export format (csv, xlsx, pdf, json)
     * @return string MIME content type
     * @throws \InvalidArgumentException When export type is not supported
     */
    public function getContentTypeForExport(string $exportType): string
    {
        $this->validateExportType($exportType);

        $contentTypes = [
            'csv' => 'text/csv',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'pdf' => 'application/pdf',
            'json' => 'application/json',
        ];

        return $contentTypes[$exportType];
    }

    /**
     * Download exported file from URL
     *
     * @param string $downloadUrl The URL to download from
     * @param string $filename The filename for the download
     * @param string $contentType The MIME content type
     * @return StreamedResponse Streamed download response
     * @throws \Exception When download fails
     */
    public function downloadExportedFile(string $downloadUrl, string $filename, string $contentType): StreamedResponse
    {
        try {
            // Handle relative URLs by prepending the export service URL
            if (!filter_var($downloadUrl, FILTER_VALIDATE_URL)) {
                $downloadUrl = rtrim($this->exportServiceUrl, '/') . '/' . ltrim($downloadUrl, '/');
            }

            Log::info('Starting file download', [
                'url' => $downloadUrl,
                'filename' => $filename,
                'content_type' => $contentType
            ]);

            return new StreamedResponse(function () use ($downloadUrl) {
                $response = Http::withOptions(['stream' => true])->get($downloadUrl);

                if ($response->failed()) {
                    throw new \Exception("Failed to download file from URL: {$downloadUrl}. Status: {$response->status()}");
                }

                $stream = $response->getBody();

                while (!$stream->eof()) {
                    echo $stream->read(8192);
                    flush();
                }
            }, 200, [
                'Content-Type' => $contentType,
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0',
            ]);
        } catch (\Exception $e) {
            Log::error('File download failed', [
                'url' => $downloadUrl,
                'filename' => $filename,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'export_service_url' => $this->exportServiceUrl,
                'context' => 'DOWNLOAD_FAILURE'
            ]);

            throw new \Exception('Failed to download exported file: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Validate the export type
     *
     * @param string $exportType
     * @throws \InvalidArgumentException
     */
    private function validateExportType(string $exportType): void
    {
        if (!in_array($exportType, $this->supportedFormats)) {
            throw new \InvalidArgumentException(
                "Invalid export type: {$exportType}. Supported types: " . implode(', ', $this->supportedFormats)
            );
        }
    }
}
