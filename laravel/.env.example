APP_NAME=laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:8000
APP_PORT=8000

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=database
DB_USERNAME=username
DB_PASSWORD=password

SFTP_HOST=
SFTP_USERNAME=
SFTP_PASSWORD=
SFTP_ROOT=uploads

DB_USERNAME_ADMIN=
DB_PASSWORD_ADMIN=

BROADCAST_DRIVER=pusher
CACHE_DRIVER=redis
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

QUEUES_NUMBER=2
QUEUES_SERVICE_NUMBER=5
QUEUES_SERVICE_PROCESSES_NUMBER=2
QUEUE_REDIS_HOST=redis
QUEUE_REDIS_PASSWORD=null
QUEUE_REDIS_PORT=6379
HORIZON_BASIC_AUTH_USERNAME=admin
HORIZON_BASIC_AUTH_PASSWORD=P@ssw0rd

MEMCACHED_HOST=memcached

REDIS_CLIENT=predis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# MAIL_MAILER=smtp
# MAIL_HOST=mailhog
# MAIL_PORT=1025
# MAIL_USERNAME=null
# MAIL_PASSWORD=null
# MAIL_ENCRYPTION=null
# MAIL_FROM_ADDRESS=null
# MAIL_FROM_NAME="${APP_NAME}"


MAIL_MAILER=smtp
MAIL_HOST=smtp.netfirms.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=Noreply_123
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false


PUSHER_APP_ID=itgates-app
PUSHER_APP_KEY=pulposoft-key
PUSHER_APP_SECRET=pulposoft-key-app-secret
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

MIX_WEBSOCKET_HOST=ws://localhost:8080
MIX_APP_NAME="${APP_NAME}"
MIX_SERVER_TURN=turn_server
MIX_SERVER_STUN=stun_server
MIX_TURN_SERVER_USERNAME=username
MIX_TURN_SERVER_CREDENTIAL=password
MIX_BASE_URL="${APP_URL}"
JWT_SECRET=tvjUQPRUSvlGBW6fOIXFlt88InF1zv6iz37dM8KrzZaY1Pd9HCjJrz7xQ2pcTTHj

UNIFIED_BASE_URL=
UNIFIED_CLIENT_ID=
UNIFIED_CLIENT_SECRET=
UNIFIED_SALT=

USERS_LIMIT=5

PLAN_ACTIONS=false

# Export Service Configuration
EXPORT_SERVICE_URL=http://localhost:7000
EXPORT_SERVICE_TIMEOUT=60
EXPORT_SERVICE_CONNECT_TIMEOUT=10
EXPORT_SERVICE_MAX_RETRIES=3
EXPORT_SERVICE_VERIFY_SSL=true
