<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],
    'twilio' => [
        'sid' => env('TWILIO_AUTH_SID'),
        'token' => env('TWILIO_AUTH_TOKEN'),
        'whatsapp_from' => env('TWILIO_WHATSAPP_FROM')
    ],

    'export_service' => [
        'url' => env('EXPORT_SERVICE_URL', 'http://localhost:7000'),
        'timeout' => env('EXPORT_SERVICE_TIMEOUT', 60),
        'connect_timeout' => env('EXPORT_SERVICE_CONNECT_TIMEOUT', 10),
        'max_retries' => env('EXPORT_SERVICE_MAX_RETRIES', 3),
        'verify_ssl' => env('EXPORT_SERVICE_VERIFY_SSL', true),
        'supported_formats' => ['csv', 'xlsx', 'pdf', 'json'],
    ],

];
