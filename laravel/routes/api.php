<?php

use App\Http\Controllers\LockScreenController;
use App\Services\UnifiedService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Route;
use Symfony\Component\HttpFoundation\StreamedResponse;
use App\Services\Externals\ExportService;
use App\User;
use App\Http\Controllers\ExportController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/





Route::post('/lockscreenunlock', [LockScreenController::class, 'post']);


Route::get('load-distributors', function () {
        $unified = new UnifiedService();

        $distributors = $unified->getDistributors();

        return response()->json(['result' => $distributors], 200);
});

Route::get('load-bricks', function () {
        $unified = new UnifiedService();

        $bricks = $unified->getBricks();

        return response()->json(['result' => $bricks], 200);
});






// ... existing routes

// Export routes
Route::prefix('exports')->group(function () {
    Route::get('/download', [ExportController::class, 'downloadExportedFile']);
    Route::post('/process-and-download', [ExportController::class, 'processAndDownload']);
});

//
//
//
//Route::get('/export/customers', function (ExportService $exportService) {
//    // Stream large dataset to microservice and receive download URL
//    $dataStream = User::cursor()->map(function ($customer) {
//        return [
//            'id' => $customer->id,
//            'name' => $customer->name,
//            'email' => $customer->email,
//        ];
//    });
//
//    $export = $exportService->streamExport($dataStream, 'csv');
//    $downloadUrl = config('services.export_service.url') . $export['download_url'];
//
//    // Now fetch the file stream and return to browser
//    $response = Http::withOptions(['stream' => true])->get($downloadUrl);
//
//    if ($response->failed()) {
//        abort(500, 'Failed to download file from export service.');
//    }
//
//    return new StreamedResponse(function () use ($response) {
//        foreach ($response->getBody() as $chunk) {
//            echo $chunk;
//        }
//    }, 200, [
//        'Content-Type' => $response->header('Content-Type', 'application/octet-stream'),
//        'Content-Disposition' => 'attachment; filename="customers.csv"',
//    ]);
//});
